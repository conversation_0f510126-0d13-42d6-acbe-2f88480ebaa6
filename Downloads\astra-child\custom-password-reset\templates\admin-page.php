<?php
/**
 * Template da página de administração
 */

if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}
?>

<div class="wrap cpr-admin-wrap">
    <h1>
        <i class="dashicons dashicons-lock"></i>
        Esqueceu Senha
    </h1>
    
    <div class="cpr-admin-header">
        <p>Configure a aparência e comportamento da tela de redefinição de senha personalizada.</p>
    </div>
    
    <div class="cpr-admin-container">
        <!-- Navegação por abas -->
        <nav class="nav-tab-wrapper cpr-nav-tabs">
            <a href="#general" class="nav-tab nav-tab-active" data-tab="general">
                <i class="dashicons dashicons-admin-generic"></i>
                Geral
            </a>
            <a href="#appearance" class="nav-tab" data-tab="appearance">
                <i class="dashicons dashicons-admin-appearance"></i>
                Aparência
            </a>
            <a href="#texts" class="nav-tab" data-tab="texts">
                <i class="dashicons dashicons-editor-textcolor"></i>
                Textos
            </a>
            <a href="#email" class="nav-tab" data-tab="email">
                <i class="dashicons dashicons-email-alt"></i>
                Email
            </a>
            <a href="#advanced" class="nav-tab" data-tab="advanced">
                <i class="dashicons dashicons-admin-tools"></i>
                Avançado
            </a>
            <a href="#preview" class="nav-tab" data-tab="preview">
                <i class="dashicons dashicons-visibility"></i>
                Visualizar
            </a>
        </nav>
        
        <!-- Formulário principal -->
        <form id="cpr-settings-form" method="post">
            <?php wp_nonce_field('cpr_admin_nonce', 'nonce'); ?>
            
            <!-- Aba Geral -->
            <div class="cpr-tab-content" id="tab-general">
                <div class="cpr-card">
                    <h2>Configurações Gerais</h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="theme">Tema Padrão</label>
                            </th>
                            <td>
                                <select name="settings[theme]" id="theme" class="regular-text">
                                    <option value="dark" <?php selected($settings['theme'], 'dark'); ?>>Escuro</option>
                                    <option value="light" <?php selected($settings['theme'], 'light'); ?>>Claro</option>
                                </select>
                                <p class="description">Tema padrão que será carregado inicialmente.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="logo_url">URL da Logo</label>
                            </th>
                            <td>
                                <input type="url" name="settings[logo_url]" id="logo_url" value="<?php echo esc_attr($settings['logo_url']); ?>" class="regular-text">
                                <button type="button" class="button cpr-upload-button" data-target="logo_url">
                                    Selecionar Imagem
                                </button>
                                <p class="description">Logo que aparecerá no topo do formulário. Se vazio, será usado um ícone padrão.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="enable_custom_page">Página Personalizada</label>
                            </th>
                            <td>
                                <label>
                                    <input type="checkbox" name="settings[enable_custom_page]" id="enable_custom_page" value="1" <?php checked($settings['enable_custom_page'], true); ?>>
                                    Usar página personalizada em vez da padrão do WordPress
                                </label>
                                <p class="description">Quando ativado, redirecionará para uma página personalizada.</p>
                            </td>
                        </tr>
                        
                        <tr class="cpr-conditional" data-condition="enable_custom_page" data-value="1">
                            <th scope="row">
                                <label for="custom_page_slug">Slug da Página</label>
                            </th>
                            <td>
                                <input type="text" name="settings[custom_page_slug]" id="custom_page_slug" value="<?php echo esc_attr($settings['custom_page_slug']); ?>" class="regular-text">
                                <p class="description">Slug da página personalizada (ex: esqueceu-senha).</p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Aba Aparência -->
            <div class="cpr-tab-content" id="tab-appearance" style="display: none;">
                <div class="cpr-card">
                    <h2>Cores e Estilo</h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="background_color">Cor de Fundo</label>
                            </th>
                            <td>
                                <input type="text" name="settings[background_color]" id="background_color" value="<?php echo esc_attr($settings['background_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo da página.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="card_background">Cor do Card</label>
                            </th>
                            <td>
                                <input type="text" name="settings[card_background]" id="card_background" value="<?php echo esc_attr($settings['card_background']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo do formulário.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="text_color">Cor do Texto</label>
                            </th>
                            <td>
                                <input type="text" name="settings[text_color]" id="text_color" value="<?php echo esc_attr($settings['text_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor principal do texto.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="button_color">Cor do Botão</label>
                            </th>
                            <td>
                                <input type="text" name="settings[button_color]" id="button_color" value="<?php echo esc_attr($settings['button_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor de fundo do botão principal.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="button_text_color">Cor do Texto do Botão</label>
                            </th>
                            <td>
                                <input type="text" name="settings[button_text_color]" id="button_text_color" value="<?php echo esc_attr($settings['button_text_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do texto do botão principal.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="button_hover_color">Cor do Botão ao Passar o Mouse</label>
                            </th>
                            <td>
                                <input type="text" name="settings[button_hover_color]" id="button_hover_color" value="<?php echo esc_attr($settings['button_hover_color']); ?>" class="cpr-color-picker">
                                <p class="description">Cor do botão quando o usuário passa o mouse por cima (hover).</p>
                            </td>
                        </tr>


                    </table>
                </div>
            </div>
            
            <!-- Aba Textos -->
            <div class="cpr-tab-content" id="tab-texts" style="display: none;">
                <div class="cpr-card">
                    <h2>Personalização de Textos</h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="title">Título Principal</label>
                            </th>
                            <td>
                                <input type="text" name="settings[title]" id="title" value="<?php echo esc_attr($settings['title']); ?>" class="regular-text">
                                <p class="description">Título que aparece no topo do formulário.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="subtitle">Subtítulo</label>
                            </th>
                            <td>
                                <textarea name="settings[subtitle]" id="subtitle" rows="3" class="large-text"><?php echo esc_textarea($settings['subtitle']); ?></textarea>
                                <p class="description">Texto explicativo abaixo do título.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="button_text">Texto do Botão</label>
                            </th>
                            <td>
                                <input type="text" name="settings[button_text]" id="button_text" value="<?php echo esc_attr($settings['button_text']); ?>" class="regular-text">
                                <p class="description">Texto do botão de envio.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">
                                <label for="back_to_login_text">Texto "Voltar ao Login"</label>
                            </th>
                            <td>
                                <input type="text" name="settings[back_to_login_text]" id="back_to_login_text" value="<?php echo esc_attr($settings['back_to_login_text']); ?>" class="regular-text">
                                <p class="description">Texto do link para voltar ao login.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="success_message">Mensagem de Sucesso</label>
                            </th>
                            <td>
                                <textarea name="settings[success_message]" id="success_message" rows="3" class="large-text"><?php echo esc_textarea($settings['success_message']); ?></textarea>
                                <p class="description">Mensagem exibida quando o e-mail de reset é enviado com sucesso.</p>
                            </td>
                        </tr>

                        <tr>
                            <th scope="row">
                                <label for="input_placeholder">Placeholder do Campo E-mail</label>
                            </th>
                            <td>
                                <input type="text" name="settings[input_placeholder]" id="input_placeholder" value="<?php echo esc_attr($settings['input_placeholder']); ?>" class="regular-text">
                                <p class="description">Texto de exemplo que aparece dentro do campo de e-mail.</p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Aba Email -->
            <div class="cpr-tab-content" id="tab-email" style="display: none;">
                <div class="cpr-email-layout">
                    <!-- Configurações -->
                    <div class="cpr-email-settings">
                        <div class="cpr-card">
                            <h2>Configurações Gerais</h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="email_enabled">Personalizar Email</label>
                                    </th>
                                    <td>
                                        <label>
                                            <input type="checkbox" name="settings[email_enabled]" id="email_enabled" value="1" <?php checked($settings['email_enabled'], true); ?>>
                                            Usar template personalizado para emails de redefinição de senha
                                        </label>
                                        <p class="description">Quando ativado, usará o template moderno em vez do email padrão do WordPress.</p>
                                    </td>
                                </tr>
                                
                                <tr class="cpr-conditional" data-condition="email_enabled" data-value="1">
                                    <th scope="row">
                                        <label for="email_subject">Assunto do Email</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_subject]" id="email_subject" value="<?php echo esc_attr($settings['email_subject']); ?>" class="regular-text cpr-preview-trigger">
                                        <p class="description">Assunto que aparecerá no email de redefinição de senha.</p>
                                    </td>
                                </tr>
                                
                                <tr class="cpr-conditional" data-condition="email_enabled" data-value="1">
                                    <th scope="row">
                                        <label for="email_from_name">Nome do Remetente</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_from_name]" id="email_from_name" value="<?php echo esc_attr($settings['email_from_name']); ?>" class="regular-text">
                                        <p class="description">Nome que aparecerá como remetente do email.</p>
                                    </td>
                                </tr>
                                
                                <tr class="cpr-conditional" data-condition="email_enabled" data-value="1">
                                    <th scope="row">
                                        <label for="email_from_email">Email do Remetente</label>
                                    </th>
                                    <td>
                                        <input type="email" name="settings[email_from_email]" id="email_from_email" value="<?php echo esc_attr($settings['email_from_email']); ?>" class="regular-text">
                                        <p class="description">Endereço de email que aparecerá como remetente.</p>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="cpr-card cpr-conditional" data-condition="email_enabled" data-value="1">
                            <h2>Aparência</h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="email_logo_url">Logo do Email</label>
                                    </th>
                                    <td>
                                        <input type="url" name="settings[email_logo_url]" id="email_logo_url" value="<?php echo esc_attr($settings['email_logo_url']); ?>" class="regular-text cpr-preview-trigger">
                                        <button type="button" class="button cpr-upload-button" data-target="email_logo_url">
                                            Selecionar Imagem
                                        </button>
                                        <p class="description">Logo que aparecerá no cabeçalho do email. Recomendado: 120px de largura.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_header_color">Cor do Cabeçalho</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_header_color]" id="email_header_color" value="<?php echo esc_attr($settings['email_header_color']); ?>" class="cpr-color-picker cpr-preview-trigger">
                                        <p class="description">Cor de fundo do cabeçalho do email.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_button_color">Cor do Botão</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_button_color]" id="email_button_color" value="<?php echo esc_attr($settings['email_button_color']); ?>" class="cpr-color-picker cpr-preview-trigger">
                                        <p class="description">Cor do botão "Redefinir Senha" no email.</p>
                                    </td>
                                </tr>
                            </table>
                        </div>

                        <div class="cpr-card cpr-conditional" data-condition="email_enabled" data-value="1">
                            <h2>Textos Personalizáveis</h2>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="email_greeting">Saudação</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_greeting]" id="email_greeting" value="<?php echo esc_attr($settings['email_greeting']); ?>" class="regular-text cpr-preview-trigger">
                                        <p class="description">Saudação inicial (ex: "Olá", "Oi", "Prezado(a)").</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_main_text">Texto Principal</label>
                                    </th>
                                    <td>
                                        <textarea name="settings[email_main_text]" id="email_main_text" rows="3" class="large-text cpr-preview-trigger"><?php echo esc_textarea($settings['email_main_text']); ?></textarea>
                                        <p class="description">Texto principal explicando a solicitação. Use {site_name} para o nome do site.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_instruction_text">Texto de Instrução</label>
                                    </th>
                                    <td>
                                        <textarea name="settings[email_instruction_text]" id="email_instruction_text" rows="2" class="large-text cpr-preview-trigger"><?php echo esc_textarea($settings['email_instruction_text']); ?></textarea>
                                        <p class="description">Instrução sobre como proceder com o botão.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_button_text">Texto do Botão</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_button_text]" id="email_button_text" value="<?php echo esc_attr($settings['email_button_text']); ?>" class="regular-text cpr-preview-trigger">
                                        <p class="description">Texto que aparecerá no botão principal.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_alternative_title">Título Link Alternativo</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_alternative_title]" id="email_alternative_title" value="<?php echo esc_attr($settings['email_alternative_title']); ?>" class="regular-text cpr-preview-trigger">
                                        <p class="description">Título da seção de link alternativo.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_alternative_text">Texto Link Alternativo</label>
                                    </th>
                                    <td>
                                        <textarea name="settings[email_alternative_text]" id="email_alternative_text" rows="2" class="large-text cpr-preview-trigger"><?php echo esc_textarea($settings['email_alternative_text']); ?></textarea>
                                        <p class="description">Explicação sobre o link alternativo.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_security_title">Título Segurança</label>
                                    </th>
                                    <td>
                                        <input type="text" name="settings[email_security_title]" id="email_security_title" value="<?php echo esc_attr($settings['email_security_title']); ?>" class="regular-text cpr-preview-trigger">
                                        <p class="description">Título da seção de informações de segurança.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_security_text">Texto de Segurança</label>
                                    </th>
                                    <td>
                                        <textarea name="settings[email_security_text]" id="email_security_text" rows="3" class="large-text cpr-preview-trigger"><?php echo esc_textarea($settings['email_security_text']); ?></textarea>
                                        <p class="description">Informações de segurança sobre o link de redefinição.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_help_text">Texto de Ajuda</label>
                                    </th>
                                    <td>
                                        <textarea name="settings[email_help_text]" id="email_help_text" rows="2" class="large-text cpr-preview-trigger"><?php echo esc_textarea($settings['email_help_text']); ?></textarea>
                                        <p class="description">Texto oferecendo ajuda adicional.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_signature">Assinatura</label>
                                    </th>
                                    <td>
                                        <textarea name="settings[email_signature]" id="email_signature" rows="2" class="large-text cpr-preview-trigger"><?php echo esc_textarea($settings['email_signature']); ?></textarea>
                                        <p class="description">Assinatura final do email. Use {site_name} para o nome do site.</p>
                                    </td>
                                </tr>
                                
                                <tr>
                                    <th scope="row">
                                        <label for="email_footer_text">Texto do Rodapé</label>
                                    </th>
                                    <td>
                                        <textarea name="settings[email_footer_text]" id="email_footer_text" rows="2" class="large-text cpr-preview-trigger"><?php echo esc_textarea($settings['email_footer_text']); ?></textarea>
                                        <p class="description">Texto que aparecerá no rodapé do email.</p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                        
                        <div class="cpr-card cpr-conditional" data-condition="email_enabled" data-value="1">
                            <h2>Teste de Email</h2>
                            <p>Envie um email de teste para verificar como ficará o template:</p>
                            
                            <table class="form-table">
                                <tr>
                                    <th scope="row">
                                        <label for="test_email">Email de Teste</label>
                                    </th>
                                    <td>
                                        <input type="email" id="test_email" value="<?php echo esc_attr(wp_get_current_user()->user_email); ?>" class="regular-text">
                                        <button type="button" class="button" id="send-test-email">
                                            <i class="dashicons dashicons-email-alt"></i>
                                            Enviar Teste
                                        </button>
                                        <p class="description">Um email de exemplo será enviado para este endereço.</p>
                                    </td>
                                </tr>
                            </table>
                        </div>
                    </div>

                    <!-- Preview -->
                    <div class="cpr-email-preview cpr-conditional" data-condition="email_enabled" data-value="1">
                        <div class="cpr-card">
                            <h2>Preview do Email</h2>
                            <p>Visualização em tempo real do template de email:</p>
                            
                            <div class="cpr-email-preview-container">
                                <iframe id="email-preview-frame" src="about:blank" frameborder="0"></iframe>
                            </div>
                            
                            <div class="cpr-preview-actions">
                                <button type="button" class="button" id="refresh-preview">
                                    <i class="dashicons dashicons-update"></i>
                                    Atualizar Preview
                                </button>
                                <button type="button" class="button" id="preview-mobile">
                                    <i class="dashicons dashicons-smartphone"></i>
                                    Visualização Mobile
                                </button>
                                <button type="button" class="button" id="preview-desktop">
                                    <i class="dashicons dashicons-desktop"></i>
                                    Visualização Desktop
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Aba Avançado -->
            <div class="cpr-tab-content" id="tab-advanced" style="display: none;">
                <div class="cpr-card">
                    <h2>Configurações Avançadas</h2>
                    
                    <table class="form-table">
                        <tr>
                            <th scope="row">CSS Personalizado</th>
                            <td>
                                <textarea name="settings[custom_css]" id="custom_css" rows="10" class="large-text code"><?php echo esc_textarea($settings['custom_css'] ?? ''); ?></textarea>
                                <p class="description">CSS personalizado que será aplicado à página de reset de senha.</p>
                            </td>
                        </tr>
                        
                        <tr>
                            <th scope="row">JavaScript Personalizado</th>
                            <td>
                                <textarea name="settings[custom_js]" id="custom_js" rows="10" class="large-text code"><?php echo esc_textarea($settings['custom_js'] ?? ''); ?></textarea>
                                <p class="description">JavaScript personalizado (sem as tags &lt;script&gt;).</p>
                            </td>
                        </tr>
                    </table>
                </div>
            </div>
            
            <!-- Aba Preview -->
            <div class="cpr-tab-content" id="tab-preview" style="display: none;">
                <div class="cpr-card">
                    <h2>Visualização</h2>
                    <p>Clique no link abaixo para visualizar a página de reset de senha:</p>

                    <p>
                        <a href="<?php echo wp_login_url() . '?action=lostpassword'; ?>"
                           target="_blank"
                           class="button button-primary button-large">
                            Visualizar Página de Reset
                        </a>
                    </p>
                </div>
            </div>
            
            <!-- Botões de ação -->
            <div class="cpr-admin-actions">
                <button type="submit" class="button button-primary button-large" id="cpr-save-settings">
                    <i class="dashicons dashicons-yes"></i>
                    Salvar Configurações
                </button>
                
                <button type="button" class="button button-secondary" id="cpr-reset-settings">
                    <i class="dashicons dashicons-undo"></i>
                    Restaurar Padrões
                </button>
                
                <div class="cpr-admin-status" id="cpr-status" style="display: none;"></div>
            </div>
        </form>
    </div>
</div>

<!-- CSS da página de administração -->
<style>
.cpr-admin-wrap {
    max-width: 1200px;
}

.cpr-admin-header {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    padding: 20px;
    margin: 20px 0;
}

.cpr-admin-container {
    background: #fff;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    margin: 20px 0;
}

.cpr-nav-tabs {
    border-bottom: 1px solid #ccd0d4;
    margin: 0;
    padding: 0 20px;
}

.cpr-nav-tabs .nav-tab {
    border-bottom: 1px solid transparent;
    margin-bottom: -1px;
}

.cpr-nav-tabs .nav-tab.nav-tab-active {
    border-bottom-color: #fff;
}

.cpr-tab-content {
    padding: 20px;
}

.cpr-card {
    background: #fff;
    border: 1px solid #e1e1e1;
    border-radius: 4px;
    padding: 20px;
    margin-bottom: 20px;
}

.cpr-card h2 {
    margin-top: 0;
    padding-bottom: 10px;
    border-bottom: 1px solid #e1e1e1;
}

.cpr-admin-actions {
    padding: 20px;
    border-top: 1px solid #ccd0d4;
    background: #f9f9f9;
}

.cpr-admin-status {
    display: inline-block;
    margin-left: 10px;
    padding: 8px 12px;
    border-radius: 4px;
    font-weight: 500;
}

.cpr-admin-status.success {
    background: #d4edda;
    color: #155724;
    border: 1px solid #c3e6cb;
}

.cpr-admin-status.error {
    background: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.cpr-conditional {
    display: none;
}

.cpr-conditional.show {
    display: table-row;
}

.cpr-preview-container {
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
    margin: 20px 0;
}

.cpr-upload-button {
    margin-left: 10px;
}

.dashicons {
    margin-right: 5px;
}

/* Layout da aba de email */
.cpr-email-layout {
    display: flex;
    gap: 30px;
    align-items: flex-start;
}

.cpr-email-settings {
    flex: 1;
    min-width: 0;
}

.cpr-email-preview {
    flex: 0 0 400px;
    position: sticky;
    top: 20px;
}

.cpr-email-preview-container {
    border: 1px solid #ccd0d4;
    border-radius: 4px;
    overflow: hidden;
    background: #f9f9f9;
    margin: 15px 0;
}

#email-preview-frame {
    width: 100%;
    height: 600px;
    background: white;
    display: block;
}

.cpr-email-preview-container.mobile #email-preview-frame {
    width: 375px;
    margin: 0 auto;
    display: block;
}

.cpr-preview-actions {
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    margin-top: 15px;
}

.cpr-preview-actions .button {
    font-size: 12px;
    padding: 4px 8px;
    height: auto;
}

/* Responsivo para telas menores */
@media (max-width: 1400px) {
    .cpr-email-layout {
        flex-direction: column;
    }
    
    .cpr-email-preview {
        flex: none;
        position: static;
        width: 100%;
    }
    
    .cpr-email-preview-container.mobile #email-preview-frame {
        width: 100%;
        max-width: 375px;
    }
}
</style>

<!-- JavaScript da página de administração -->
<script>
jQuery(document).ready(function($) {
    // Inicializar color pickers
    $('.cpr-color-picker').wpColorPicker();
    
    // Navegação por abas
    $('.cpr-nav-tabs .nav-tab').on('click', function(e) {
        e.preventDefault();
        
        const tab = $(this).data('tab');
        
        // Atualizar abas ativas
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');
        
        // Mostrar conteúdo da aba
        $('.cpr-tab-content').hide();
        $('#tab-' + tab).show();
        
        // Atualizar URL
        window.location.hash = tab;
    });
    
    // Carregar aba da URL
    if (window.location.hash) {
        const hash = window.location.hash.substring(1);
        $('.nav-tab[data-tab="' + hash + '"]').click();
    }
    
    // Campos condicionais
    function toggleConditionalFields() {
        $('.cpr-conditional').each(function() {
            const $row = $(this);
            const condition = $row.data('condition');
            const value = $row.data('value');
            const $field = $('#' + condition);
            
            if ($field.is(':checkbox')) {
                if (($field.is(':checked') && value == '1') || (!$field.is(':checked') && value == '0')) {
                    $row.addClass('show');
                } else {
                    $row.removeClass('show');
                }
            } else {
                if ($field.val() == value) {
                    $row.addClass('show');
                } else {
                    $row.removeClass('show');
                }
            }
        });
    }
    
    // Executar ao carregar e quando campos mudarem
    toggleConditionalFields();
    $('input, select').on('change', toggleConditionalFields);
    
    // Upload de imagem
    $('.cpr-upload-button').on('click', function(e) {
        e.preventDefault();
        
        const button = $(this);
        const targetField = button.data('target');
        
        const mediaUploader = wp.media({
            title: 'Selecionar Logo',
            button: {
                text: 'Usar esta imagem'
            },
            multiple: false
        });
        
        mediaUploader.on('select', function() {
            const attachment = mediaUploader.state().get('selection').first().toJSON();
            $('#' + targetField).val(attachment.url);
        });
        
        mediaUploader.open();
    });
    
    // Salvar configurações
    $('#cpr-settings-form').on('submit', function(e) {
        e.preventDefault();
        
        const $form = $(this);
        const $button = $('#cpr-save-settings');
        const $status = $('#cpr-status');
        
        $button.prop('disabled', true).find('.dashicons').addClass('spin');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: $form.serialize() + '&action=cpr_save_settings',
            success: function(response) {
                if (response.success) {
                    $status.removeClass('error').addClass('success').text(response.data).show();
                } else {
                    $status.removeClass('success').addClass('error').text(response.data || 'Erro ao salvar').show();
                }
            },
            error: function() {
                $status.removeClass('success').addClass('error').text('Erro de conexão').show();
            },
            complete: function() {
                $button.prop('disabled', false).find('.dashicons').removeClass('spin');
                setTimeout(function() {
                    $status.fadeOut();
                }, 3000);
            }
        });
    });
    
    // Restaurar padrões
    $('#cpr-reset-settings').on('click', function() {
        if (confirm('Tem certeza que deseja restaurar todas as configurações para os valores padrão?')) {
            const $button = $(this);
            const $status = $('#cpr-status');
            
            $button.prop('disabled', true);
            
            $.ajax({
                url: ajaxurl,
                type: 'POST',
                data: {
                    action: 'cpr_reset_settings',
                    nonce: $('input[name="nonce"]').val()
                },
                success: function(response) {
                    if (response.success) {
                        $status.removeClass('error').addClass('success').text(response.data).show();
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        $status.removeClass('success').addClass('error').text(response.data || 'Erro ao restaurar').show();
                    }
                },
                error: function() {
                    $status.removeClass('success').addClass('error').text('Erro de conexão').show();
                },
                complete: function() {
                    $button.prop('disabled', false);
                    setTimeout(function() {
                        $status.fadeOut();
                    }, 3000);
                }
            });
        }
    });
    
    // Enviar email de teste
    $('#send-test-email').on('click', function() {
        const $button = $(this);
        const $status = $('#cpr-status');
        const testEmail = $('#test_email').val();
        
        if (!testEmail) {
            alert('Por favor, insira um endereço de email válido.');
            return;
        }
        
        $button.prop('disabled', true).find('.dashicons').addClass('spin');
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cpr_send_test_email',
                email: testEmail,
                nonce: $('input[name="nonce"]').val()
            },
            success: function(response) {
                if (response.success) {
                    $status.removeClass('error').addClass('success').text('Email de teste enviado com sucesso!').show();
                } else {
                    $status.removeClass('success').addClass('error').text(response.data || 'Erro ao enviar email').show();
                }
            },
            error: function() {
                $status.removeClass('success').addClass('error').text('Erro de conexão').show();
            },
            complete: function() {
                $button.prop('disabled', false).find('.dashicons').removeClass('spin');
                setTimeout(function() {
                    $status.fadeOut();
                }, 3000);
            }
        });
    });
    
    // Preview do email em tempo real
    function updateEmailPreview() {
        if (!$('#email_enabled').is(':checked')) {
            return;
        }
        
        const settings = {
            email_logo_url: $('#email_logo_url').val(),
            email_header_color: $('#email_header_color').val(),
            email_button_color: $('#email_button_color').val(),
            email_greeting: $('#email_greeting').val(),
            email_main_text: $('#email_main_text').val(),
            email_instruction_text: $('#email_instruction_text').val(),
            email_button_text: $('#email_button_text').val(),
            email_alternative_title: $('#email_alternative_title').val(),
            email_alternative_text: $('#email_alternative_text').val(),
            email_security_title: $('#email_security_title').val(),
            email_security_text: $('#email_security_text').val(),
            email_help_text: $('#email_help_text').val(),
            email_signature: $('#email_signature').val(),
            email_footer_text: $('#email_footer_text').val()
        };
        
        $.ajax({
            url: ajaxurl,
            type: 'POST',
            data: {
                action: 'cpr_generate_email_preview',
                settings: settings,
                nonce: $('input[name="nonce"]').val()
            },
            success: function(response) {
                if (response.success) {
                    const iframe = document.getElementById('email-preview-frame');
                    const doc = iframe.contentDocument || iframe.contentWindow.document;
                    doc.open();
                    doc.write(response.data);
                    doc.close();
                }
            }
        });
    }
    
    // Atualizar preview quando campos mudarem
    $('.cpr-preview-trigger').on('input change', function() {
        clearTimeout(window.previewTimeout);
        window.previewTimeout = setTimeout(updateEmailPreview, 500);
    });
    
    // Botão de atualizar preview
    $('#refresh-preview').on('click', updateEmailPreview);
    
    // Alternar visualização mobile/desktop
    $('#preview-mobile').on('click', function() {
        $('.cpr-email-preview-container').addClass('mobile');
        $(this).addClass('button-primary').siblings().removeClass('button-primary');
    });
    
    $('#preview-desktop').on('click', function() {
        $('.cpr-email-preview-container').removeClass('mobile');
        $(this).addClass('button-primary').siblings().removeClass('button-primary');
    });
    
    // Inicializar preview quando a aba for carregada
    $('.nav-tab[data-tab="email"]').on('click', function() {
        setTimeout(function() {
            if ($('#email_enabled').is(':checked')) {
                updateEmailPreview();
            }
        }, 100);
    });
    
    // Atualizar preview quando email for habilitado
    $('#email_enabled').on('change', function() {
        if ($(this).is(':checked')) {
            setTimeout(updateEmailPreview, 100);
        }
    });
});
</script>
